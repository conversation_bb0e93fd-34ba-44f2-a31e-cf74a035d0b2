{"name": "@kdt-sol/pumpfun-sdk", "type": "module", "version": "0.4.0", "packageManager": "pnpm@10.15.1", "description": "A SDK to interact with <PERSON>ump<PERSON>un", "author": "<PERSON><PERSON> <<EMAIL>>", "license": "MIT", "homepage": "https://github.com/kdt-sol/pumpfun-sdk", "repository": "github:kdt-sol/pumpfun-sdk", "bugs": {"email": "<EMAIL>", "url": "https://github.com/kdt-sol/pumpfun-sdk/issues"}, "sideEffects": false, "exports": {".": {"types": "./dist/types/index.d.ts", "require": "./dist/index.cjs", "default": "./dist/index.js"}}, "main": "dist/index.js", "types": "dist/types/index.d.ts", "files": ["dist", "src"], "publishConfig": {"access": "public"}, "scripts": {"generate": "tsx scripts/generate.ts", "build": "rimraf dist && tsup && tsc --project ./tsconfig.build.json", "release": "tsx scripts/release.ts && changelogen gh release && pnpm publish", "up": "ncu -i", "lint": "eslint .", "lint:fix": "eslint . --fix", "typecheck": "tsc --noEmit", "prepare": "npx only-allow pnpm && simple-git-hooks && pnpm generate", "prepublishOnly": "pnpm build"}, "peerDependencies": {"@solana/kit": "^3.0"}, "dependencies": {"@kdt310722/utils": "^0.0.19", "@solana-program/compute-budget": "^0.8.0", "@solana-program/token": "^0.5.1"}, "devDependencies": {"@codama/nodes-from-anchor": "^1.2.5", "@codama/renderers": "^1.0.31", "@codama/renderers-js": "^1.3.4", "@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "@kdt310722/eslint-config": "^0.2.0", "@kdt310722/tsconfig": "^1.0.0", "@solana/kit": "^3.0.1", "@swc/core": "^1.13.5", "@types/node": "^24.3.0", "bs58": "^6.0.0", "changelogen": "^0.6.2", "codama": "^1.3.3", "eslint": "^9.34.0", "execa": "^9.6.0", "lint-staged": "^16.1.6", "npm-check-updates": "^18.0.3", "only-allow": "^1.2.1", "rimraf": "^6.0.1", "simple-git-hooks": "^2.13.1", "tsup": "^8.5.0", "tsx": "^4.20.5", "typescript": "^5.9.2"}, "commitlint": {"extends": "@commitlint/config-conventional"}, "pnpm": {"ignoredBuiltDependencies": ["@kdt310722/eslint-config", "@kdt310722/utils"], "onlyBuiltDependencies": ["@swc/core", "esbuild", "simple-git-hooks", "unrs-resolver"]}, "simple-git-hooks": {"commit-msg": "npx --no -- commitlint --edit ${1}", "pre-commit": "npx lint-staged"}, "lint-staged": {"*": "eslint --fix"}}