import { address } from '@solana/kit'

export const BONDING_CURVE_SEED = Buffer.from('bonding-curve')

export const CREATOR_VAULT_SEED = Buffer.from('creator-vault')

export const GLOBAL_VOLUME_ACCUMULATOR_SEED = Buffer.from('global_volume_accumulator')

export const USER_VOLUME_ACCUMULATOR_SEED = Buffer.from('user_volume_accumulator')

export const SYSTEM_PROGRAM_ADDRESS = address('11111111111111111111111111111111')

export const TOKEN_PROGRAM_ADDRESS = address('TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA')

export const ASSOCIATED_TOKEN_PROGRAM_ADDRESS = address('ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL')

export const RENT_PROGRAM_ADDRESS = address('SysvarRent111111111111111111111111111111111')

export const GLOBAL_ACCOUNT_ADDRESS = address('4wTV1YmiEkRvAtNtsSGPtUrqRYQMe5SKy2uB4Jjaxnjf')

export const FEE_RECIPIENT_ADDRESS = address('62qc2CNXwrYqQScmEdiZFFAnJR262PxWEuNQtxfafNgV')

export const EVENT_AUTHORITY_ADDRESS = address('Ce6TQqeHC9p8KetsN6JsjHK7UTZk7nasjjnr7XxXp9F1')

export const MICRO_LAMPORTS_PER_LAMPORT = 10 ** 6

export const PUMP_TOKEN_DECIMALS = 6
