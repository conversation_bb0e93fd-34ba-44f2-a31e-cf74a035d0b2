1{
2  "address": "6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P",
3  "metadata": {
4    "name": "pump",
5    "version": "0.1.0",
6    "spec": "0.1.0",
7    "description": "Created with Anchor"
8  },
9  "instructions": [
10    {
11      "name": "admin_set_creator",
12      "docs": [
13        "Allows Global::admin_set_creator_authority to override the bonding curve creator"
14      ],
15      "discriminator": [
16        69,
17        25,
18        171,
19        142,
20        57,
21        239,
22        13,
23        4
24      ],
25      "accounts": [
26        {
27          "name": "admin_set_creator_authority",
28          "signer": true,
29          "relations": [
30            "global"
31          ]
32        },
33        {
34          "name": "global",
35          "pda": {
36            "seeds": [
37              {
38                "kind": "const",
39                "value": [
40                  103,
41                  108,
42                  111,
43                  98,
44                  97,
45                  108
46                ]
47              }
48            ]
49          }
50        },
51        {
52          "name": "mint"
53        },
54        {
55          "name": "bonding_curve",
56          "writable": true,
57          "pda": {
58            "seeds": [
59              {
60                "kind": "const",
61                "value": [
62                  98,
63                  111,
64                  110,
65                  100,
66                  105,
67                  110,
68                  103,
69                  45,
70                  99,
71                  117,
72                  114,
73                  118,
74                  101
75                ]
76              },
77              {
78                "kind": "account",
79                "path": "mint"
80              }
81            ]
82          }
83        },
84        {
85          "name": "event_authority",
86          "pda": {
87            "seeds": [
88              {
89                "kind": "const",
90                "value": [
91                  95,
92                  95,
93                  101,
94                  118,
95                  101,
96                  110,
97                  116,
98                  95,
99                  97,
100                  117,
101                  116,
102                  104,
103                  111,
104                  114,
105                  105,
106                  116,
107                  121
108                ]
109              }
110            ]
111          }
112        },
113        {
114          "name": "program"
115        }
116      ],
117      "args": [
118        {
119          "name": "creator",
120          "type": "pubkey"
121        }
122      ]
123    },
124    {
125      "name": "admin_set_idl_authority",
126      "discriminator": [
127        8,
128        217,
129        96,
130        231,
131        144,
132        104,
133        192,
134        5
135      ],
136      "accounts": [
137        {
138          "name": "authority",
139          "signer": true,
140          "relations": [
141            "global"
142          ]
143        },
144        {
145          "name": "global",
146          "pda": {
147            "seeds": [
148              {
149                "kind": "const",
150                "value": [
151                  103,
152                  108,
153                  111,
154                  98,
155                  97,
156                  108
157                ]
158              }
159            ]
160          }
161        },
162        {
163          "name": "idl_account",
164          "writable": true
165        },
166        {
167          "name": "system_program",
168          "address": "11111111111111111111111111111111"
169        },
170        {
171          "name": "program_signer",
172          "pda": {
173            "seeds": []
174          }
175        },
176        {
177          "name": "event_authority",
178          "pda": {
179            "seeds": [
180              {
181                "kind": "const",
182                "value": [
183                  95,
184                  95,
185                  101,
186                  118,
187                  101,
188                  110,
189                  116,
190                  95,
191                  97,
192                  117,
193                  116,
194                  104,
195                  111,
196                  114,
197                  105,
198                  116,
199                  121
200                ]
201              }
202            ]
203          }
204        },
205        {
206          "name": "program"
207        }
208      ],
209      "args": [
210        {
211          "name": "idl_authority",
212          "type": "pubkey"
213        }
214      ]
215    },
216    {
217      "name": "admin_update_token_incentives",
218      "discriminator": [
219        209,
220        11,
221        115,
222        87,
223        213,
224        23,
225        124,
226        204
227      ],
228      "accounts": [
229        {
230          "name": "authority",
231          "writable": true,
232          "signer": true,
233          "relations": [
234            "global"
235          ]
236        },
237        {
238          "name": "global",
239          "pda": {
240            "seeds": [
241              {
242                "kind": "const",
243                "value": [
244                  103,
245                  108,
246                  111,
247                  98,
248                  97,
249                  108
250                ]
251              }
252            ]
253          }
254        },
255        {
256          "name": "global_volume_accumulator",
257          "writable": true,
258          "pda": {
259            "seeds": [
260              {
261                "kind": "const",
262                "value": [
263                  103,
264                  108,
265                  111,
266                  98,
267                  97,
268                  108,
269                  95,
270                  118,
271                  111,
272                  108,
273                  117,
274                  109,
275                  101,
276                  95,
277                  97,
278                  99,
279                  99,
280                  117,
281                  109,
282                  117,
283                  108,
284                  97,
285                  116,
286                  111,
287                  114
288                ]
289              }
290            ]
291          }
292        },
293        {
294          "name": "mint"
295        },
296        {
297          "name": "global_incentive_token_account",
298          "writable": true,
299          "pda": {
300            "seeds": [
301              {
302                "kind": "account",
303                "path": "global_volume_accumulator"
304              },
305              {
306                "kind": "account",
307                "path": "token_program"
308              },
309              {
310                "kind": "account",
311                "path": "mint"
312              }
313            ],
314            "program": {
315              "kind": "const",
316              "value": [
317                140,
318                151,
319                37,
320                143,
321                78,
322                36,
323                137,
324                241,
325                187,
326                61,
327                16,
328                41,
329                20,
330                142,
331                13,
332                131,
333                11,
334                90,
335                19,
336                153,
337                218,
338                255,
339                16,
340                132,
341                4,
342                142,
343                123,
344                216,
345                219,
346                233,
347                248,
348                89
349              ]
350            }
351          }
352        },
353        {
354          "name": "associated_token_program",
355          "address": "ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL"
356        },
357        {
358          "name": "system_program",
359          "address": "11111111111111111111111111111111"
360        },
361        {
362          "name": "token_program"
363        },
364        {
365          "name": "event_authority",
366          "pda": {
367            "seeds": [
368              {
369                "kind": "const",
370                "value": [
371                  95,
372                  95,
373                  101,
374                  118,
375                  101,
376                  110,
377                  116,
378                  95,
379                  97,
380                  117,
381                  116,
382                  104,
383                  111,
384                  114,
385                  105,
386                  116,
387                  121
388                ]
389              }
390            ]
391          }
392        },
393        {
394          "name": "program"
395        }
396      ],
397      "args": [
398        {
399          "name": "start_time",
400          "type": "i64"
401        },
402        {
403          "name": "end_time",
404          "type": "i64"
405        },
406        {
407          "name": "seconds_in_a_day",
408          "type": "i64"
409        },
410        {
411          "name": "day_number",
412          "type": "u64"
413        },
414        {
415          "name": "pump_token_supply_per_day",
416          "type": "u64"
417        }
418      ]
419    },
420    {
421      "name": "buy",
422      "docs": [
423        "Buys tokens from a bonding curve."
424      ],
425      "discriminator": [
426        102,
427        6,
428        61,
429        18,
430        1,
431        218,
432        235,
433        234
434      ],
435      "accounts": [
436        {
437          "name": "global",
438          "pda": {
439            "seeds": [
440              {
441                "kind": "const",
442                "value": [
443                  103,
444                  108,
445                  111,
446                  98,
447                  97,
448                  108
449                ]
450              }
451            ]
452          }
453        },
454        {
455          "name": "fee_recipient",
456          "writable": true
457        },
458        {
459          "name": "mint"
460        },
461        {
462          "name": "bonding_curve",
463          "writable": true,
464          "pda": {
465            "seeds": [
466              {
467                "kind": "const",
468                "value": [
469                  98,
470                  111,
471                  110,
472                  100,
473                  105,
474                  110,
475                  103,
476                  45,
477                  99,
478                  117,
479                  114,
480                  118,
481                  101
482                ]
483              },
484              {
485                "kind": "account",
486                "path": "mint"
487              }
488            ]
489          }
490        },
491        {
492          "name": "associated_bonding_curve",
493          "writable": true,
494          "pda": {
495            "seeds": [
496              {
497                "kind": "account",
498                "path": "bonding_curve"
499              },
500              {
501                "kind": "const",
502                "value": [
503                  6,
504                  221,
505                  246,
506                  225,
507                  215,
508                  101,
509                  161,
510                  147,
511                  217,
512                  203,
513                  225,
514                  70,
515                  206,
516                  235,
517                  121,
518                  172,
519                  28,
520                  180,
521                  133,
522                  237,
523                  95,
524                  91,
525                  55,
526                  145,
527                  58,
528                  140,
529                  245,
530                  133,
531                  126,
532                  255,
533                  0,
534                  169
535                ]
536              },
537              {
538                "kind": "account",
539                "path": "mint"
540              }
541            ],
542            "program": {
543              "kind": "const",
544              "value": [
545                140,
546                151,
547                37,
548                143,
549                78,
550                36,
551                137,
552                241,
553                187,
554                61,
555                16,
556                41,
557                20,
558                142,
559                13,
560                131,
561                11,
562                90,
563                19,
564                153,
565                218,
566                255,
567                16,
568                132,
569                4,
570                142,
571                123,
572                216,
573                219,
574                233,
575                248,
576                89
577              ]
578            }
579          }
580        },
581        {
582          "name": "associated_user",
583          "writable": true
584        },
585        {
586          "name": "user",
587          "writable": true,
588          "signer": true
589        },
590        {
591          "name": "system_program",
592          "address": "11111111111111111111111111111111"
593        },
594        {
595          "name": "token_program",
596          "address": "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA"
597        },
598        {
599          "name": "creator_vault",
600          "writable": true,
601          "pda": {
602            "seeds": [
603              {
604                "kind": "const",
605                "value": [
606                  99,
607                  114,
608                  101,
609                  97,
610                  116,
611                  111,
612                  114,
613                  45,
614                  118,
615                  97,
616                  117,
617                  108,
618                  116
619                ]
620              },
621              {
622                "kind": "account",
623                "path": "bonding_curve.creator",
624                "account": "BondingCurve"
625              }
626            ]
627          }
628        },
629        {
630          "name": "event_authority",
631          "pda": {
632            "seeds": [
633              {
634                "kind": "const",
635                "value": [
636                  95,
637                  95,
638                  101,
639                  118,
640                  101,
641                  110,
642                  116,
643                  95,
644                  97,
645                  117,
646                  116,
647                  104,
648                  111,
649                  114,
650                  105,
651                  116,
652                  121
653                ]
654              }
655            ]
656          }
657        },
658        {
659          "name": "program",
660          "address": "6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P"
661        },
662        {
663          "name": "global_volume_accumulator",
664          "writable": true,
665          "pda": {
666            "seeds": [
667              {
668                "kind": "const",
669                "value": [
670                  103,
671                  108,
672                  111,
673                  98,
674                  97,
675                  108,
676                  95,
677                  118,
678                  111,
679                  108,
680                  117,
681                  109,
682                  101,
683                  95,
684                  97,
685                  99,
686                  99,
687                  117,
688                  109,
689                  117,
690                  108,
691                  97,
692                  116,
693                  111,
694                  114
695                ]
696              }
697            ]
698          }
699        },
700        {
701          "name": "user_volume_accumulator",
702          "writable": true,
703          "pda": {
704            "seeds": [
705              {
706                "kind": "const",
707                "value": [
708                  117,
709                  115,
710                  101,
711                  114,
712                  95,
713                  118,
714                  111,
715                  108,
716                  117,
717                  109,
718                  101,
719                  95,
720                  97,
721                  99,
722                  99,
723                  117,
724                  109,
725                  117,
726                  108,
727                  97,
728                  116,
729                  111,
730                  114
731                ]
732              },
733              {
734                "kind": "account",
735                "path": "user"
736              }
737            ]
738          }
739        },
740        {
741          "name": "fee_config",
742          "pda": {
743            "seeds": [
744              {
745                "kind": "const",
746                "value": [
747                  102,
748                  101,
749                  101,
750                  95,
751                  99,
752                  111,
753                  110,
754                  102,
755                  105,
756                  103
757                ]
758              },
759              {
760                "kind": "const",
761                "value": [
762                  1,
763                  86,
764                  224,
765                  246,
766                  147,
767                  102,
768                  90,
769                  207,
770                  68,
771                  219,
772                  21,
773                  104,
774                  191,
775                  23,
776                  91,
777                  170,
778                  81,
779                  137,
780                  203,
781                  151,
782                  245,
783                  210,
784                  255,
785                  59,
786                  101,
787                  93,
788                  43,
789                  182,
790                  253,
791                  109,
792                  24,
793                  176
794                ]
795              }
796            ],
797            "program": {
798              "kind": "account",
799              "path": "fee_program"
800            }
801          }
802        },
803        {
804          "name": "fee_program",
805          "address": "pfeeUxB6jkeY1Hxd7CsFCAjcbHA9rWtchMGdZ6VojVZ"
806        }
807      ],
808      "args": [
809        {
810          "name": "amount",
811          "type": "u64"
812        },
813        {
814          "name": "max_sol_cost",
815          "type": "u64"
816        },
817        {
818          "name": "track_volume",
819          "type": {
820            "defined": {
821              "name": "OptionBool"
822            }
823          }
824        }
825      ]
826    },
827    {
828      "name": "claim_token_incentives",
829      "discriminator": [
830        16,
831        4,
832        71,
833        28,
834        204,
835        1,
836        40,
837        27
838      ],
839      "accounts": [
840        {
841          "name": "user"
842        },
843        {
844          "name": "user_ata",
845          "writable": true,
846          "pda": {
847            "seeds": [
848              {
849                "kind": "account",
850                "path": "user"
851              },
852              {
853                "kind": "account",
854                "path": "token_program"
855              },
856              {
857                "kind": "account",
858                "path": "mint"
859              }
860            ],
861            "program": {
862              "kind": "const",
863              "value": [
864                140,
865                151,
866                37,
867                143,
868                78,
869                36,
870                137,
871                241,
872                187,
873                61,
874                16,
875                41,
876                20,
877                142,
878                13,
879                131,
880                11,
881                90,
882                19,
883                153,
884                218,
885                255,
886                16,
887                132,
888                4,
889                142,
890                123,
891                216,
892                219,
893                233,
894                248,
895                89
896              ]
897            }
898          }
899        },
900        {
901          "name": "global_volume_accumulator",
902          "pda": {
903            "seeds": [
904              {
905                "kind": "const",
906                "value": [
907                  103,
908                  108,
909                  111,
910                  98,
911                  97,
912                  108,
913                  95,
914                  118,
915                  111,
916                  108,
917                  117,
918                  109,
919                  101,
920                  95,
921                  97,
922                  99,
923                  99,
924                  117,
925                  109,
926                  117,
927                  108,
928                  97,
929                  116,
930                  111,
931                  114
932                ]
933              }
934            ]
935          }
936        },
937        {
938          "name": "global_incentive_token_account",
939          "writable": true,
940          "pda": {
941            "seeds": [
942              {
943                "kind": "account",
944                "path": "global_volume_accumulator"
945              },
946              {
947                "kind": "account",
948                "path": "token_program"
949              },
950              {
951                "kind": "account",
952                "path": "mint"
953              }
954            ],
955            "program": {
956              "kind": "const",
957              "value": [
958                140,
959                151,
960                37,
961                143,
962                78,
963                36,
964                137,
965                241,
966                187,
967                61,
968                16,
969                41,
970                20,
971                142,
972                13,
973                131,
974                11,
975                90,
976                19,
977                153,
978                218,
979                255,
980                16,
981                132,
982                4,
983                142,
984                123,
985                216,
986                219,
987                233,
988                248,
989                89
990              ]
991            }
992          }
993        },
994        {
995          "name": "user_volume_accumulator",
996          "writable": true,
997          "pda": {
998            "seeds": [
999              {
1000                "kind": "const",
1001                "value": [
1002                  117,
1003                  115,
1004                  101,
1005                  114,
1006                  95,
1007                  118,
1008                  111,
1009                  108,
1010                  117,
1011                  109,
1012                  101,
1013                  95,
1014                  97,
1015                  99,
1016                  99,
1017                  117,
1018                  109,
1019                  117,
1020                  108,
1021                  97,
1022                  116,
1023                  111,
1024                  114
1025                ]
1026              },
1027              {
1028                "kind": "account",
1029                "path": "user"
1030              }
1031            ]
1032          }
1033        },
1034        {
1035          "name": "mint",
1036          "relations": [
1037            "global_volume_accumulator"
1038          ]
1039        },
1040        {
1041          "name": "token_program"
1042        },
1043        {
1044          "name": "system_program",
1045          "address": "11111111111111111111111111111111"
1046        },
1047        {
1048          "name": "associated_token_program",
1049          "address": "ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL"
1050        },
1051        {
1052          "name": "event_authority",
1053          "pda": {
1054            "seeds": [
1055              {
1056                "kind": "const",
1057                "value": [
1058                  95,
1059                  95,
1060                  101,
1061                  118,
1062                  101,
1063                  110,
1064                  116,
1065                  95,
1066                  97,
1067                  117,
1068                  116,
1069                  104,
1070                  111,
1071                  114,
1072                  105,
1073                  116,
1074                  121
1075                ]
1076              }
1077            ]
1078          }
1079        },
1080        {
1081          "name": "program",
1082          "address": "6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P"
1083        },
1084        {
1085          "name": "payer",
1086          "writable": true,
1087          "signer": true
1088        }
1089      ],
1090      "args": []
1091    },
1092    {
1093      "name": "close_user_volume_accumulator",
1094      "discriminator": [
1095        249,
1096        69,
1097        164,
1098        218,
1099        150,
1100        103,
1101        84,
1102        138
1103      ],
1104      "accounts": [
1105        {
1106          "name": "user",
1107          "writable": true,
1108          "signer": true
1109        },
1110        {
1111          "name": "user_volume_accumulator",
1112          "writable": true,
1113          "pda": {
1114            "seeds": [
1115              {
1116                "kind": "const",
1117                "value": [
1118                  117,
1119                  115,
1120                  101,
1121                  114,
1122                  95,
1123                  118,
1124                  111,
1125                  108,
1126                  117,
1127                  109,
1128                  101,
1129                  95,
1130                  97,
1131                  99,
1132                  99,
1133                  117,
1134                  109,
1135                  117,
1136                  108,
1137                  97,
1138                  116,
1139                  111,
1140                  114
1141                ]
1142              },
1143              {
1144                "kind": "account",
1145                "path": "user"
1146              }
1147            ]
1148          }
1149        },
1150        {
1151          "name": "event_authority",
1152          "pda": {
1153            "seeds": [
1154              {
1155                "kind": "const",
1156                "value": [
1157                  95,
1158                  95,
1159                  101,
1160                  118,
1161                  101,
1162                  110,
1163                  116,
1164                  95,
1165                  97,
1166                  117,
1167                  116,
1168                  104,
1169                  111,
1170                  114,
1171                  105,
1172                  116,
1173                  121
1174                ]
1175              }
1176            ]
1177          }
1178        },
1179        {
1180          "name": "program"
1181        }
1182      ],
1183      "args": []
1184    },
1185    {
1186      "name": "collect_creator_fee",
1187      "docs": [
1188        "Collects creator_fee from creator_vault to the coin creator account"
1189      ],
1190      "discriminator": [
1191        20,
1192        22,
1193        86,
1194        123,
1195        198,
1196        28,
1197        219,
1198        132
1199      ],
1200      "accounts": [
1201        {
1202          "name": "creator",
1203          "writable": true
1204        },
1205        {
1206          "name": "creator_vault",
1207          "writable": true,
1208          "pda": {
1209            "seeds": [
1210              {
1211                "kind": "const",
1212                "value": [
1213                  99,
1214                  114,
1215                  101,
1216                  97,
1217                  116,
1218                  111,
1219                  114,
1220                  45,
1221                  118,
1222                  97,
1223                  117,
1224                  108,
1225                  116
1226                ]
1227              },
1228              {
1229                "kind": "account",
1230                "path": "creator"
1231              }
1232            ]
1233          }
1234        },
1235        {
1236          "name": "system_program",
1237          "address": "11111111111111111111111111111111"
1238        },
1239        {
1240          "name": "event_authority",
1241          "pda": {
1242            "seeds": [
1243              {
1244                "kind": "const",
1245                "value": [
1246                  95,
1247                  95,
1248                  101,
1249                  118,
1250                  101,
1251                  110,
1252                  116,
1253                  95,
1254                  97,
1255                  117,
1256                  116,
1257                  104,
1258                  111,
1259                  114,
1260                  105,
1261                  116,
1262                  121
1263                ]
1264              }
1265            ]
1266          }
1267        },
1268        {
1269          "name": "program"
1270        }
1271      ],
1272      "args": []
1273    },
1274    {
1275      "name": "create",
1276      "docs": [
1277        "Creates a new coin and bonding curve."
1278      ],
1279      "discriminator": [
1280        24,
1281        30,
1282        200,
1283        40,
1284        5,
1285        28,
1286        7,
1287        119
1288      ],
1289      "accounts": [
1290        {
1291          "name": "mint",
1292          "writable": true,
1293          "signer": true
1294        },
1295        {
1296          "name": "mint_authority",
1297          "pda": {
1298            "seeds": [
1299              {
1300                "kind": "const",
1301                "value": [
1302                  109,
1303                  105,
1304                  110,
1305                  116,
1306                  45,
1307                  97,
1308                  117,
1309                  116,
1310                  104,
1311                  111,
1312                  114,
1313                  105,
1314                  116,
1315                  121
1316                ]
1317              }
1318            ]
1319          }
1320        },
1321        {
1322          "name": "bonding_curve",
1323          "writable": true,
1324          "pda": {
1325            "seeds": [
1326              {
1327                "kind": "const",
1328                "value": [
1329                  98,
1330                  111,
1331                  110,
1332                  100,
1333                  105,
1334                  110,
1335                  103,
1336                  45,
1337                  99,
1338                  117,
1339                  114,
1340                  118,
1341                  101
1342                ]
1343              },
1344              {
1345                "kind": "account",
1346                "path": "mint"
1347              }
1348            ]
1349          }
1350        },
1351        {
1352          "name": "associated_bonding_curve",
1353          "writable": true,
1354          "pda": {
1355            "seeds": [
1356              {
1357                "kind": "account",
1358                "path": "bonding_curve"
1359              },
1360              {
1361                "kind": "const",
1362                "value": [
1363                  6,
1364                  221,
1365                  246,
1366                  225,
1367                  215,
1368                  101,
1369                  161,
1370                  147,
1371                  217,
1372                  203,
1373                  225,
1374                  70,
1375                  206,
1376                  235,
1377                  121,
1378                  172,
1379                  28,
1380                  180,
1381                  133,
1382                  237,
1383                  95,
1384                  91,
1385                  55,
1386                  145,
1387                  58,
1388                  140,
1389                  245,
1390                  133,
1391                  126,
1392                  255,
1393                  0,
1394                  169
1395                ]
1396              },
1397              {
1398                "kind": "account",
1399                "path": "mint"
1400              }
1401            ],
1402            "program": {
1403              "kind": "const",
1404              "value": [
1405                140,
1406                151,
1407                37,
1408                143,
1409                78,
1410                36,
1411                137,
1412                241,
1413                187,
1414                61,
1415                16,
1416                41,
1417                20,
1418                142,
1419                13,
1420                131,
1421                11,
1422                90,
1423                19,
1424                153,
1425                218,
1426                255,
1427                16,
1428                132,
1429                4,
1430                142,
1431                123,
1432                216,
1433                219,
1434                233,
1435                248,
1436                89
1437              ]
1438            }
1439          }
1440        },
1441        {
1442          "name": "global",
1443          "pda": {
1444            "seeds": [
1445              {
1446                "kind": "const",
1447                "value": [
1448                  103,
1449                  108,
1450                  111,
1451                  98,
1452                  97,
1453                  108
1454                ]
1455              }
1456            ]
1457          }
1458        },
1459        {
1460          "name": "mpl_token_metadata",
1461          "address": "metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s"
1462        },
1463        {
1464          "name": "metadata",
1465          "writable": true,
1466          "pda": {
1467            "seeds": [
1468              {
1469                "kind": "const",
1470                "value": [
1471                  109,
1472                  101,
1473                  116,
1474                  97,
1475                  100,
1476                  97,
1477                  116,
1478                  97
1479                ]
1480              },
1481              {
1482                "kind": "const",
1483                "value": [
1484                  11,
1485                  112,
1486                  101,
1487                  177,
1488                  227,
1489                  209,
1490                  124,
1491                  69,
1492                  56,
1493                  157,
1494                  82,
1495                  127,
1496                  107,
1497                  4,
1498                  195,
1499                  205,
1500                  88,
1501                  184,
1502                  108,
1503                  115,
1504                  26,
1505                  160,
1506                  253,
1507                  181,
1508                  73,
1509                  182,
1510                  209,
1511                  188,
1512                  3,
1513                  248,
1514                  41,
1515                  70
1516                ]
1517              },
1518              {
1519                "kind": "account",
1520                "path": "mint"
1521              }
1522            ],
1523            "program": {
1524              "kind": "account",
1525              "path": "mpl_token_metadata"
1526            }
1527          }
1528        },
1529        {
1530          "name": "user",
1531          "writable": true,
1532          "signer": true
1533        },
1534        {
1535          "name": "system_program",
1536          "address": "11111111111111111111111111111111"
1537        },
1538        {
1539          "name": "token_program",
1540          "address": "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA"
1541        },
1542        {
1543          "name": "associated_token_program",
1544          "address": "ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL"
1545        },
1546        {
1547          "name": "rent",
1548          "address": "SysvarRent111111111111111111111111111111111"
1549        },
1550        {
1551          "name": "event_authority",
1552          "pda": {
1553            "seeds": [
1554              {
1555                "kind": "const",
1556                "value": [
1557                  95,
1558                  95,
1559                  101,
1560                  118,
1561                  101,
1562                  110,
1563                  116,
1564                  95,
1565                  97,
1566                  117,
1567                  116,
1568                  104,
1569                  111,
1570                  114,
1571                  105,
1572                  116,
1573                  121
1574                ]
1575              }
1576            ]
1577          }
1578        },
1579        {
1580          "name": "program"
1581        }
1582      ],
1583      "args": [
1584        {
1585          "name": "name",
1586          "type": "string"
1587        },
1588        {
1589          "name": "symbol",
1590          "type": "string"
1591        },
1592        {
1593          "name": "uri",
1594          "type": "string"
1595        },
1596        {
1597          "name": "creator",
1598          "type": "pubkey"
1599        }
1600      ]
1601    },
1602    {
1603      "name": "extend_account",
1604      "docs": [
1605        "Extends the size of program-owned accounts"
1606      ],
1607      "discriminator": [
1608        234,
1609        102,
1610        194,
1611        203,
1612        150,
1613        72,
1614        62,
1615        229
1616      ],
1617      "accounts": [
1618        {
1619          "name": "account",
1620          "writable": true
1621        },
1622        {
1623          "name": "user",
1624          "signer": true
1625        },
1626        {
1627          "name": "system_program",
1628          "address": "11111111111111111111111111111111"
1629        },
1630        {
1631          "name": "event_authority",
1632          "pda": {
1633            "seeds": [
1634              {
1635                "kind": "const",
1636                "value": [
1637                  95,
1638                  95,
1639                  101,
1640                  118,
1641                  101,
1642                  110,
1643                  116,
1644                  95,
1645                  97,
1646                  117,
1647                  116,
1648                  104,
1649                  111,
1650                  114,
1651                  105,
1652                  116,
1653                  121
1654                ]
1655              }
1656            ]
1657          }
1658        },
1659        {
1660          "name": "program"
1661        }
1662      ],
1663      "args": []
1664    },
1665    {
1666      "name": "init_user_volume_accumulator",
1667      "discriminator": [
1668        94,
1669        6,
1670        202,
1671        115,
1672        255,
1673        96,
1674        232,
1675        183
1676      ],
1677      "accounts": [
1678        {
1679          "name": "payer",
1680          "writable": true,
1681          "signer": true
1682        },
1683        {
1684          "name": "user"
1685        },
1686        {
1687          "name": "user_volume_accumulator",
1688          "writable": true,
1689          "pda": {
1690            "seeds": [
1691              {
1692                "kind": "const",
1693                "value": [
1694                  117,
1695                  115,
1696                  101,
1697                  114,
1698                  95,
1699                  118,
1700                  111,
1701                  108,
1702                  117,
1703                  109,
1704                  101,
1705                  95,
1706                  97,
1707                  99,
1708                  99,
1709                  117,
1710                  109,
1711                  117,
1712                  108,
1713                  97,
1714                  116,
1715                  111,
1716                  114
1717                ]
1718              },
1719              {
1720                "kind": "account",
1721                "path": "user"
1722              }
1723            ]
1724          }
1725        },
1726        {
1727          "name": "system_program",
1728          "address": "11111111111111111111111111111111"
1729        },
1730        {
1731          "name": "event_authority",
1732          "pda": {
1733            "seeds": [
1734              {
1735                "kind": "const",
1736                "value": [
1737                  95,
1738                  95,
1739                  101,
1740                  118,
1741                  101,
1742                  110,
1743                  116,
1744                  95,
1745                  97,
1746                  117,
1747                  116,
1748                  104,
1749                  111,
1750                  114,
1751                  105,
1752                  116,
1753                  121
1754                ]
1755              }
1756            ]
1757          }
1758        },
1759        {
1760          "name": "program"
1761        }
1762      ],
1763      "args": []
1764    },
1765    {
1766      "name": "initialize",
1767      "docs": [
1768        "Creates the global state."
1769      ],
1770      "discriminator": [
1771        175,
1772        175,
1773        109,
1774        31,
1775        13,
1776        152,
1777        155,
1778        237
1779      ],
1780      "accounts": [
1781        {
1782          "name": "global",
1783          "writable": true,
1784          "pda": {
1785            "seeds": [
1786              {
1787                "kind": "const",
1788                "value": [
1789                  103,
1790                  108,
1791                  111,
1792                  98,
1793                  97,
1794                  108
1795                ]
1796              }
1797            ]
1798          }
1799        },
1800        {
1801          "name": "user",
1802          "writable": true,
1803          "signer": true
1804        },
1805        {
1806          "name": "system_program",
1807          "address": "11111111111111111111111111111111"
1808        }
1809      ],
1810      "args": []
1811    },
1812    {
1813      "name": "migrate",
1814      "docs": [
1815        "Migrates liquidity to pump_amm if the bonding curve is complete"
1816      ],
1817      "discriminator": [
1818        155,
1819        234,
1820        231,
1821        146,
1822        236,
1823        158,
1824        162,
1825        30
1826      ],
1827      "accounts": [
1828        {
1829          "name": "global",
1830          "pda": {
1831            "seeds": [
1832              {
1833                "kind": "const",
1834                "value": [
1835                  103,
1836                  108,
1837                  111,
1838                  98,
1839                  97,
1840                  108
1841                ]
1842              }
1843            ]
1844          }
1845        },
1846        {
1847          "name": "withdraw_authority",
1848          "writable": true,
1849          "relations": [
1850            "global"
1851          ]
1852        },
1853        {
1854          "name": "mint"
1855        },
1856        {
1857          "name": "bonding_curve",
1858          "writable": true,
1859          "pda": {
1860            "seeds": [
1861              {
1862                "kind": "const",
1863                "value": [
1864                  98,
1865                  111,
1866                  110,
1867                  100,
1868                  105,
1869                  110,
1870                  103,
1871                  45,
1872                  99,
1873                  117,
1874                  114,
1875                  118,
1876                  101
1877                ]
1878              },
1879              {
1880                "kind": "account",
1881                "path": "mint"
1882              }
1883            ]
1884          }
1885        },
1886        {
1887          "name": "associated_bonding_curve",
1888          "writable": true,
1889          "pda": {
1890            "seeds": [
1891              {
1892                "kind": "account",
1893                "path": "bonding_curve"
1894              },
1895              {
1896                "kind": "const",
1897                "value": [
1898                  6,
1899                  221,
1900                  246,
1901                  225,
1902                  215,
1903                  101,
1904                  161,
1905                  147,
1906                  217,
1907                  203,
1908                  225,
1909                  70,
1910                  206,
1911                  235,
1912                  121,
1913                  172,
1914                  28,
1915                  180,
1916                  133,
1917                  237,
1918                  95,
1919                  91,
1920                  55,
1921                  145,
1922                  58,
1923                  140,
1924                  245,
1925                  133,
1926                  126,
1927                  255,
1928                  0,
1929                  169
1930                ]
1931              },
1932              {
1933                "kind": "account",
1934                "path": "mint"
1935              }
1936            ],
1937            "program": {
1938              "kind": "const",
1939              "value": [
1940                140,
1941                151,
1942                37,
1943                143,
1944                78,
1945                36,
1946                137,
1947                241,
1948                187,
1949                61,
1950                16,
1951                41,
1952                20,
1953                142,
1954                13,
1955                131,
1956                11,
1957                90,
1958                19,
1959                153,
1960                218,
1961                255,
1962                16,
1963                132,
1964                4,
1965                142,
1966                123,
1967                216,
1968                219,
1969                233,
1970                248,
1971                89
1972              ]
1973            }
1974          }
1975        },
1976        {
1977          "name": "user",
1978          "signer": true
1979        },
1980        {
1981          "name": "system_program",
1982          "address": "11111111111111111111111111111111"
1983        },
1984        {
1985          "name": "token_program",
1986          "address": "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA"
1987        },
1988        {
1989          "name": "pump_amm",
1990          "address": "pAMMBay6oceH9fJKBRHGP5D4bD4sWpmSwMn52FMfXEA"
1991        },
1992        {
1993          "name": "pool",
1994          "writable": true,
1995          "pda": {
1996            "seeds": [
1997              {
1998                "kind": "const",
1999                "value": [
2000                  112,
2001                  111,
2002                  111,
2003                  108
2004                ]
2005              },
2006              {
2007                "kind": "const",
2008                "value": [
2009                  0,
2010                  0
2011                ]
2012              },
2013              {
2014                "kind": "account",
2015                "path": "pool_authority"
2016              },
2017              {
2018                "kind": "account",
2019                "path": "mint"
2020              },
2021              {
2022                "kind": "account",
2023                "path": "wsol_mint"
2024              }
2025            ],
2026            "program": {
2027              "kind": "account",
2028              "path": "pump_amm"
2029            }
2030          }
2031        },
2032        {
2033          "name": "pool_authority",
2034          "writable": true,
2035          "pda": {
2036            "seeds": [
2037              {
2038                "kind": "const",
2039                "value": [
2040                  112,
2041                  111,
2042                  111,
2043                  108,
2044                  45,
2045                  97,
2046                  117,
2047                  116,
2048                  104,
2049                  111,
2050                  114,
2051                  105,
2052                  116,
2053                  121
2054                ]
2055              },
2056              {
2057                "kind": "account",
2058                "path": "mint"
2059              }
2060            ]
2061          }
2062        },
2063        {
2064          "name": "pool_authority_mint_account",
2065          "writable": true,
2066          "pda": {
2067            "seeds": [
2068              {
2069                "kind": "account",
2070                "path": "pool_authority"
2071              },
2072              {
2073                "kind": "account",
2074                "path": "token_program"
2075              },
2076              {
2077                "kind": "account",
2078                "path": "mint"
2079              }
2080            ],
2081            "program": {
2082              "kind": "account",
2083              "path": "associated_token_program"
2084            }
2085          }
2086        },
2087        {
2088          "name": "pool_authority_wsol_account",
2089          "writable": true,
2090          "pda": {
2091            "seeds": [
2092              {
2093                "kind": "account",
2094                "path": "pool_authority"
2095              },
2096              {
2097                "kind": "account",
2098                "path": "token_program"
2099              },
2100              {
2101                "kind": "account",
2102                "path": "wsol_mint"
2103              }
2104            ],
2105            "program": {
2106              "kind": "account",
2107              "path": "associated_token_program"
2108            }
2109          }
2110        },
2111        {
2112          "name": "amm_global_config",
2113          "pda": {
2114            "seeds": [
2115              {
2116                "kind": "const",
2117                "value": [
2118                  103,
2119                  108,
2120                  111,
2121                  98,
2122                  97,
2123                  108,
2124                  95,
2125                  99,
2126                  111,
2127                  110,
2128                  102,
2129                  105,
2130                  103
2131                ]
2132              }
2133            ],
2134            "program": {
2135              "kind": "account",
2136              "path": "pump_amm"
2137            }
2138          }
2139        },
2140        {
2141          "name": "wsol_mint",
2142          "address": "So11111111111111111111111111111111111111112"
2143        },
2144        {
2145          "name": "lp_mint",
2146          "writable": true,
2147          "pda": {
2148            "seeds": [
2149              {
2150                "kind": "const",
2151                "value": [
2152                  112,
2153                  111,
2154                  111,
2155                  108,
2156                  95,
2157                  108,
2158                  112,
2159                  95,
2160                  109,
2161                  105,
2162                  110,
2163                  116
2164                ]
2165              },
2166              {
2167                "kind": "account",
2168                "path": "pool"
2169              }
2170            ],
2171            "program": {
2172              "kind": "account",
2173              "path": "pump_amm"
2174            }
2175          }
2176        },
2177        {
2178          "name": "user_pool_token_account",
2179          "writable": true,
2180          "pda": {
2181            "seeds": [
2182              {
2183                "kind": "account",
2184                "path": "pool_authority"
2185              },
2186              {
2187                "kind": "account",
2188                "path": "token_2022_program"
2189              },
2190              {
2191                "kind": "account",
2192                "path": "lp_mint"
2193              }
2194            ],
2195            "program": {
2196              "kind": "account",
2197              "path": "associated_token_program"
2198            }
2199          }
2200        },
2201        {
2202          "name": "pool_base_token_account",
2203          "writable": true,
2204          "pda": {
2205            "seeds": [
2206              {
2207                "kind": "account",
2208                "path": "pool"
2209              },
2210              {
2211                "kind": "account",
2212                "path": "token_program"
2213              },
2214              {
2215                "kind": "account",
2216                "path": "mint"
2217              }
2218            ],
2219            "program": {
2220              "kind": "account",
2221              "path": "associated_token_program"
2222            }
2223          }
2224        },
2225        {
2226          "name": "pool_quote_token_account",
2227          "writable": true,
2228          "pda": {
2229            "seeds": [
2230              {
2231                "kind": "account",
2232                "path": "pool"
2233              },
2234              {
2235                "kind": "account",
2236                "path": "token_program"
2237              },
2238              {
2239                "kind": "account",
2240                "path": "wsol_mint"
2241              }
2242            ],
2243            "program": {
2244              "kind": "account",
2245              "path": "associated_token_program"
2246            }
2247          }
2248        },
2249        {
2250          "name": "token_2022_program",
2251          "address": "TokenzQdBNbLqP5VEhdkAS6EPFLC1PHnBqCXEpPxuEb"
2252        },
2253        {
2254          "name": "associated_token_program",
2255          "address": "ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL"
2256        },
2257        {
2258          "name": "pump_amm_event_authority",
2259          "pda": {
2260            "seeds": [
2261              {
2262                "kind": "const",
2263                "value": [
2264                  95,
2265                  95,
2266                  101,
2267                  118,
2268                  101,
2269                  110,
2270                  116,
2271                  95,
2272                  97,
2273                  117,
2274                  116,
2275                  104,
2276                  111,
2277                  114,
2278                  105,
2279                  116,
2280                  121
2281                ]
2282              }
2283            ],
2284            "program": {
2285              "kind": "account",
2286              "path": "pump_amm"
2287            }
2288          }
2289        },
2290        {
2291          "name": "event_authority",
2292          "pda": {
2293            "seeds": [
2294              {
2295                "kind": "const",
2296                "value": [
2297                  95,
2298                  95,
2299                  101,
2300                  118,
2301                  101,
2302                  110,
2303                  116,
2304                  95,
2305                  97,
2306                  117,
2307                  116,
2308                  104,
2309                  111,
2310                  114,
2311                  105,
2312                  116,
2313                  121
2314                ]
2315              }
2316            ]
2317          }
2318        },
2319        {
2320          "name": "program"
2321        }
2322      ],
2323      "args": []
2324    },
2325    {
2326      "name": "sell",
2327      "docs": [
2328        "Sells tokens into a bonding curve."
2329      ],
2330      "discriminator": [
2331        51,
2332        230,
2333        133,
2334        164,
2335        1,
2336        127,
2337        131,
2338        173
2339      ],
2340      "accounts": [
2341        {
2342          "name": "global",
2343          "pda": {
2344            "seeds": [
2345              {
2346                "kind": "const",
2347                "value": [
2348                  103,
2349                  108,
2350                  111,
2351                  98,
2352                  97,
2353                  108
2354                ]
2355              }
2356            ]
2357          }
2358        },
2359        {
2360          "name": "fee_recipient",
2361          "writable": true
2362        },
2363        {
2364          "name": "mint"
2365        },
2366        {
2367          "name": "bonding_curve",
2368          "writable": true,
2369          "pda": {
2370            "seeds": [
2371              {
2372                "kind": "const",
2373                "value": [
2374                  98,
2375                  111,
2376                  110,
2377                  100,
2378                  105,
2379                  110,
2380                  103,
2381                  45,
2382                  99,
2383                  117,
2384                  114,
2385                  118,
2386                  101
2387                ]
2388              },
2389              {
2390                "kind": "account",
2391                "path": "mint"
2392              }
2393            ]
2394          }
2395        },
2396        {
2397          "name": "associated_bonding_curve",
2398          "writable": true,
2399          "pda": {
2400            "seeds": [
2401              {
2402                "kind": "account",
2403                "path": "bonding_curve"
2404              },
2405              {
2406                "kind": "const",
2407                "value": [
2408                  6,
2409                  221,
2410                  246,
2411                  225,
2412                  215,
2413                  101,
2414                  161,
2415                  147,
2416                  217,
2417                  203,
2418                  225,
2419                  70,
2420                  206,
2421                  235,
2422                  121,
2423                  172,
2424                  28,
2425                  180,
2426                  133,
2427                  237,
2428                  95,
2429                  91,
2430                  55,
2431                  145,
2432                  58,
2433                  140,
2434                  245,
2435                  133,
2436                  126,
2437                  255,
2438                  0,
2439                  169
2440                ]
2441              },
2442              {
2443                "kind": "account",
2444                "path": "mint"
2445              }
2446            ],
2447            "program": {
2448              "kind": "const",
2449              "value": [
2450                140,
2451                151,
2452                37,
2453                143,
2454                78,
2455                36,
2456                137,
2457                241,
2458                187,
2459                61,
2460                16,
2461                41,
2462                20,
2463                142,
2464                13,
2465                131,
2466                11,
2467                90,
2468                19,
2469                153,
2470                218,
2471                255,
2472                16,
2473                132,
2474                4,
2475                142,
2476                123,
2477                216,
2478                219,
2479                233,
2480                248,
2481                89
2482              ]
2483            }
2484          }
2485        },
2486        {
2487          "name": "associated_user",
2488          "writable": true
2489        },
2490        {
2491          "name": "user",
2492          "writable": true,
2493          "signer": true
2494        },
2495        {
2496          "name": "system_program",
2497          "address": "11111111111111111111111111111111"
2498        },
2499        {
2500          "name": "creator_vault",
2501          "writable": true,
2502          "pda": {
2503            "seeds": [
2504              {
2505                "kind": "const",
2506                "value": [
2507                  99,
2508                  114,
2509                  101,
2510                  97,
2511                  116,
2512                  111,
2513                  114,
2514                  45,
2515                  118,
2516                  97,
2517                  117,
2518                  108,
2519                  116
2520                ]
2521              },
2522              {
2523                "kind": "account",
2524                "path": "bonding_curve.creator",
2525                "account": "BondingCurve"
2526              }
2527            ]
2528          }
2529        },
2530        {
2531          "name": "token_program",
2532          "address": "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA"
2533        },
2534        {
2535          "name": "event_authority",
2536          "pda": {
2537            "seeds": [
2538              {
2539                "kind": "const",
2540                "value": [
2541                  95,
2542                  95,
2543                  101,
2544                  118,
2545                  101,
2546                  110,
2547                  116,
2548                  95,
2549                  97,
2550                  117,
2551                  116,
2552                  104,
2553                  111,
2554                  114,
2555                  105,
2556                  116,
2557                  121
2558                ]
2559              }
2560            ]
2561          }
2562        },
2563        {
2564          "name": "program",
2565          "address": "6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P"
2566        },
2567        {
2568          "name": "fee_config",
2569          "pda": {
2570            "seeds": [
2571              {
2572                "kind": "const",
2573                "value": [
2574                  102,
2575                  101,
2576                  101,
2577                  95,
2578                  99,
2579                  111,
2580                  110,
2581                  102,
2582                  105,
2583                  103
2584                ]
2585              },
2586              {
2587                "kind": "const",
2588                "value": [
2589                  1,
2590                  86,
2591                  224,
2592                  246,
2593                  147,
2594                  102,
2595                  90,
2596                  207,
2597                  68,
2598                  219,
2599                  21,
2600                  104,
2601                  191,
2602                  23,
2603                  91,
2604                  170,
2605                  81,
2606                  137,
2607                  203,
2608                  151,
2609                  245,
2610                  210,
2611                  255,
2612                  59,
2613                  101,
2614                  93,
2615                  43,
2616                  182,
2617                  253,
2618                  109,
2619                  24,
2620                  176
2621                ]
2622              }
2623            ],
2624            "program": {
2625              "kind": "account",
2626              "path": "fee_program"
2627            }
2628          }
2629        },
2630        {
2631          "name": "fee_program",
2632          "address": "pfeeUxB6jkeY1Hxd7CsFCAjcbHA9rWtchMGdZ6VojVZ"
2633        }
2634      ],
2635      "args": [
2636        {
2637          "name": "amount",
2638          "type": "u64"
2639        },
2640        {
2641          "name": "min_sol_output",
2642          "type": "u64"
2643        }
2644      ]
2645    },
2646    {
2647      "name": "set_creator",
2648      "docs": [
2649        "Allows Global::set_creator_authority to set the bonding curve creator from Metaplex metadata or input argument"
2650      ],
2651      "discriminator": [
2652        254,
2653        148,
2654        255,
2655        112,
2656        207,
2657        142,
2658        170,
2659        165
2660      ],
2661      "accounts": [
2662        {
2663          "name": "set_creator_authority",
2664          "signer": true,
2665          "relations": [
2666            "global"
2667          ]
2668        },
2669        {
2670          "name": "global",
2671          "pda": {
2672            "seeds": [
2673              {
2674                "kind": "const",
2675                "value": [
2676                  103,
2677                  108,
2678                  111,
2679                  98,
2680                  97,
2681                  108
2682                ]
2683              }
2684            ]
2685          }
2686        },
2687        {
2688          "name": "mint"
2689        },
2690        {
2691          "name": "metadata",
2692          "pda": {
2693            "seeds": [
2694              {
2695                "kind": "const",
2696                "value": [
2697                  109,
2698                  101,
2699                  116,
2700                  97,
2701                  100,
2702                  97,
2703                  116,
2704                  97
2705                ]
2706              },
2707              {
2708                "kind": "const",
2709                "value": [
2710                  11,
2711                  112,
2712                  101,
2713                  177,
2714                  227,
2715                  209,
2716                  124,
2717                  69,
2718                  56,
2719                  157,
2720                  82,
2721                  127,
2722                  107,
2723                  4,
2724                  195,
2725                  205,
2726                  88,
2727                  184,
2728                  108,
2729                  115,
2730                  26,
2731                  160,
2732                  253,
2733                  181,
2734                  73,
2735                  182,
2736                  209,
2737                  188,
2738                  3,
2739                  248,
2740                  41,
2741                  70
2742                ]
2743              },
2744              {
2745                "kind": "account",
2746                "path": "mint"
2747              }
2748            ],
2749            "program": {
2750              "kind": "const",
2751              "value": [
2752                11,
2753                112,
2754                101,
2755                177,
2756                227,
2757                209,
2758                124,
2759                69,
2760                56,
2761                157,
2762                82,
2763                127,
2764                107,
2765                4,
2766                195,
2767                205,
2768                88,
2769                184,
2770                108,
2771                115,
2772                26,
2773                160,
2774                253,
2775                181,
2776                73,
2777                182,
2778                209,
2779                188,
2780                3,
2781                248,
2782                41,
2783                70
2784              ]
2785            }
2786          }
2787        },
2788        {
2789          "name": "bonding_curve",
2790          "writable": true,
2791          "pda": {
2792            "seeds": [
2793              {
2794                "kind": "const",
2795                "value": [
2796                  98,
2797                  111,
2798                  110,
2799                  100,
2800                  105,
2801                  110,
2802                  103,
2803                  45,
2804                  99,
2805                  117,
2806                  114,
2807                  118,
2808                  101
2809                ]
2810              },
2811              {
2812                "kind": "account",
2813                "path": "mint"
2814              }
2815            ]
2816          }
2817        },
2818        {
2819          "name": "event_authority",
2820          "pda": {
2821            "seeds": [
2822              {
2823                "kind": "const",
2824                "value": [
2825                  95,
2826                  95,
2827                  101,
2828                  118,
2829                  101,
2830                  110,
2831                  116,
2832                  95,
2833                  97,
2834                  117,
2835                  116,
2836                  104,
2837                  111,
2838                  114,
2839                  105,
2840                  116,
2841                  121
2842                ]
2843              }
2844            ]
2845          }
2846        },
2847        {
2848          "name": "program"
2849        }
2850      ],
2851      "args": [
2852        {
2853          "name": "creator",
2854          "type": "pubkey"
2855        }
2856      ]
2857    },
2858    {
2859      "name": "set_metaplex_creator",
2860      "docs": [
2861        "Syncs the bonding curve creator with the Metaplex metadata creator if it exists"
2862      ],
2863      "discriminator": [
2864        138,
2865        96,
2866        174,
2867        217,
2868        48,
2869        85,
2870        197,
2871        246
2872      ],
2873      "accounts": [
2874        {
2875          "name": "mint"
2876        },
2877        {
2878          "name": "metadata",
2879          "pda": {
2880            "seeds": [
2881              {
2882                "kind": "const",
2883                "value": [
2884                  109,
2885                  101,
2886                  116,
2887                  97,
2888                  100,
2889                  97,
2890                  116,
2891                  97
2892                ]
2893              },
2894              {
2895                "kind": "const",
2896                "value": [
2897                  11,
2898                  112,
2899                  101,
2900                  177,
2901                  227,
2902                  209,
2903                  124,
2904                  69,
2905                  56,
2906                  157,
2907                  82,
2908                  127,
2909                  107,
2910                  4,
2911                  195,
2912                  205,
2913                  88,
2914                  184,
2915                  108,
2916                  115,
2917                  26,
2918                  160,
2919                  253,
2920                  181,
2921                  73,
2922                  182,
2923                  209,
2924                  188,
2925                  3,
2926                  248,
2927                  41,
2928                  70
2929                ]
2930              },
2931              {
2932                "kind": "account",
2933                "path": "mint"
2934              }
2935            ],
2936            "program": {
2937              "kind": "const",
2938              "value": [
2939                11,
2940                112,
2941                101,
2942                177,
2943                227,
2944                209,
2945                124,
2946                69,
2947                56,
2948                157,
2949                82,
2950                127,
2951                107,
2952                4,
2953                195,
2954                205,
2955                88,
2956                184,
2957                108,
2958                115,
2959                26,
2960                160,
2961                253,
2962                181,
2963                73,
2964                182,
2965                209,
2966                188,
2967                3,
2968                248,
2969                41,
2970                70
2971              ]
2972            }
2973          }
2974        },
2975        {
2976          "name": "bonding_curve",
2977          "writable": true,
2978          "pda": {
2979            "seeds": [
2980              {
2981                "kind": "const",
2982                "value": [
2983                  98,
2984                  111,
2985                  110,
2986                  100,
2987                  105,
2988                  110,
2989                  103,
2990                  45,
2991                  99,
2992                  117,
2993                  114,
2994                  118,
2995                  101
2996                ]
2997              },
2998              {
2999                "kind": "account",
3000                "path": "mint"
3001              }
3002            ]
3003          }
3004        },
3005        {
3006          "name": "event_authority",
3007          "pda": {
3008            "seeds": [
3009              {
3010                "kind": "const",
3011                "value": [
3012                  95,
3013                  95,
3014                  101,
3015                  118,
3016                  101,
3017                  110,
3018                  116,
3019                  95,
3020                  97,
3021                  117,
3022                  116,
3023                  104,
3024                  111,
3025                  114,
3026                  105,
3027                  116,
3028                  121
3029                ]
3030              }
3031            ]
3032          }
3033        },
3034        {
3035          "name": "program"
3036        }
3037      ],
3038      "args": []
3039    },
3040    {
3041      "name": "set_params",
3042      "docs": [
3043        "Sets the global state parameters."
3044      ],
3045      "discriminator": [
3046        27,
3047        234,
3048        178,
3049        52,
3050        147,
3051        2,
3052        187,
3053        141
3054      ],
3055      "accounts": [
3056        {
3057          "name": "global",
3058          "writable": true,
3059          "pda": {
3060            "seeds": [
3061              {
3062                "kind": "const",
3063                "value": [
3064                  103,
3065                  108,
3066                  111,
3067                  98,
3068                  97,
3069                  108
3070                ]
3071              }
3072            ]
3073          }
3074        },
3075        {
3076          "name": "authority",
3077          "writable": true,
3078          "signer": true,
3079          "relations": [
3080            "global"
3081          ]
3082        },
3083        {
3084          "name": "event_authority",
3085          "pda": {
3086            "seeds": [
3087              {
3088                "kind": "const",
3089                "value": [
3090                  95,
3091                  95,
3092                  101,
3093                  118,
3094                  101,
3095                  110,
3096                  116,
3097                  95,
3098                  97,
3099                  117,
3100                  116,
3101                  104,
3102                  111,
3103                  114,
3104                  105,
3105                  116,
3106                  121
3107                ]
3108              }
3109            ]
3110          }
3111        },
3112        {
3113          "name": "program"
3114        }
3115      ],
3116      "args": [
3117        {
3118          "name": "initial_virtual_token_reserves",
3119          "type": "u64"
3120        },
3121        {
3122          "name": "initial_virtual_sol_reserves",
3123          "type": "u64"
3124        },
3125        {
3126          "name": "initial_real_token_reserves",
3127          "type": "u64"
3128        },
3129        {
3130          "name": "token_total_supply",
3131          "type": "u64"
3132        },
3133        {
3134          "name": "fee_basis_points",
3135          "type": "u64"
3136        },
3137        {
3138          "name": "withdraw_authority",
3139          "type": "pubkey"
3140        },
3141        {
3142          "name": "enable_migrate",
3143          "type": "bool"
3144        },
3145        {
3146          "name": "pool_migration_fee",
3147          "type": "u64"
3148        },
3149        {
3150          "name": "creator_fee_basis_points",
3151          "type": "u64"
3152        },
3153        {
3154          "name": "set_creator_authority",
3155          "type": "pubkey"
3156        },
3157        {
3158          "name": "admin_set_creator_authority",
3159          "type": "pubkey"
3160        }
3161      ]
3162    },
3163    {
3164      "name": "sync_user_volume_accumulator",
3165      "discriminator": [
3166        86,
3167        31,
3168        192,
3169        87,
3170        163,
3171        87,
3172        79,
3173        238
3174      ],
3175      "accounts": [
3176        {
3177          "name": "user"
3178        },
3179        {
3180          "name": "global_volume_accumulator",
3181          "pda": {
3182            "seeds": [
3183              {
3184                "kind": "const",
3185                "value": [
3186                  103,
3187                  108,
3188                  111,
3189                  98,
3190                  97,
3191                  108,
3192                  95,
3193                  118,
3194                  111,
3195                  108,
3196                  117,
3197                  109,
3198                  101,
3199                  95,
3200                  97,
3201                  99,
3202                  99,
3203                  117,
3204                  109,
3205                  117,
3206                  108,
3207                  97,
3208                  116,
3209                  111,
3210                  114
3211                ]
3212              }
3213            ]
3214          }
3215        },
3216        {
3217          "name": "user_volume_accumulator",
3218          "writable": true,
3219          "pda": {
3220            "seeds": [
3221              {
3222                "kind": "const",
3223                "value": [
3224                  117,
3225                  115,
3226                  101,
3227                  114,
3228                  95,
3229                  118,
3230                  111,
3231                  108,
3232                  117,
3233                  109,
3234                  101,
3235                  95,
3236                  97,
3237                  99,
3238                  99,
3239                  117,
3240                  109,
3241                  117,
3242                  108,
3243                  97,
3244                  116,
3245                  111,
3246                  114
3247                ]
3248              },
3249              {
3250                "kind": "account",
3251                "path": "user"
3252              }
3253            ]
3254          }
3255        },
3256        {
3257          "name": "event_authority",
3258          "pda": {
3259            "seeds": [
3260              {
3261                "kind": "const",
3262                "value": [
3263                  95,
3264                  95,
3265                  101,
3266                  118,
3267                  101,
3268                  110,
3269                  116,
3270                  95,
3271                  97,
3272                  117,
3273                  116,
3274                  104,
3275                  111,
3276                  114,
3277                  105,
3278                  116,
3279                  121
3280                ]
3281              }
3282            ]
3283          }
3284        },
3285        {
3286          "name": "program"
3287        }
3288      ],
3289      "args": []
3290    },
3291    {
3292      "name": "update_global_authority",
3293      "discriminator": [
3294        227,
3295        181,
3296        74,
3297        196,
3298        208,
3299        21,
3300        97,
3301        213
3302      ],
3303      "accounts": [
3304        {
3305          "name": "global",
3306          "writable": true,
3307          "pda": {
3308            "seeds": [
3309              {
3310                "kind": "const",
3311                "value": [
3312                  103,
3313                  108,
3314                  111,
3315                  98,
3316                  97,
3317                  108
3318                ]
3319              }
3320            ]
3321          }
3322        },
3323        {
3324          "name": "authority",
3325          "signer": true,
3326          "relations": [
3327            "global"
3328          ]
3329        },
3330        {
3331          "name": "new_authority"
3332        },
3333        {
3334          "name": "event_authority",
3335          "pda": {
3336            "seeds": [
3337              {
3338                "kind": "const",
3339                "value": [
3340                  95,
3341                  95,
3342                  101,
3343                  118,
3344                  101,
3345                  110,
3346                  116,
3347                  95,
3348                  97,
3349                  117,
3350                  116,
3351                  104,
3352                  111,
3353                  114,
3354                  105,
3355                  116,
3356                  121
3357                ]
3358              }
3359            ]
3360          }
3361        },
3362        {
3363          "name": "program"
3364        }
3365      ],
3366      "args": []
3367    }
3368  ],
3369  "accounts": [
3370    {
3371      "name": "BondingCurve",
3372      "discriminator": [
3373        23,
3374        183,
3375        248,
3376        55,
3377        96,
3378        216,
3379        172,
3380        96
3381      ]
3382    },
3383    {
3384      "name": "FeeConfig",
3385      "discriminator": [
3386        143,
3387        52,
3388        146,
3389        187,
3390        219,
3391        123,
3392        76,
3393        155
3394      ]
3395    },
3396    {
3397      "name": "Global",
3398      "discriminator": [
3399        167,
3400        232,
3401        232,
3402        177,
3403        200,
3404        108,
3405        114,
3406        127
3407      ]
3408    },
3409    {
3410      "name": "GlobalVolumeAccumulator",
3411      "discriminator": [
3412        202,
3413        42,
3414        246,
3415        43,
3416        142,
3417        190,
3418        30,
3419        255
3420      ]
3421    },
3422    {
3423      "name": "UserVolumeAccumulator",
3424      "discriminator": [
3425        86,
3426        255,
3427        112,
3428        14,
3429        102,
3430        53,
3431        154,
3432        250
3433      ]
3434    }
3435  ],
3436  "events": [
3437    {
3438      "name": "AdminSetCreatorEvent",
3439      "discriminator": [
3440        64,
3441        69,
3442        192,
3443        104,
3444        29,
3445        30,
3446        25,
3447        107
3448      ]
3449    },
3450    {
3451      "name": "AdminSetIdlAuthorityEvent",
3452      "discriminator": [
3453        245,
3454        59,
3455        70,
3456        34,
3457        75,
3458        185,
3459        109,
3460        92
3461      ]
3462    },
3463    {
3464      "name": "AdminUpdateTokenIncentivesEvent",
3465      "discriminator": [
3466        147,
3467        250,
3468        108,
3469        120,
3470        247,
3471        29,
3472        67,
3473        222
3474      ]
3475    },
3476    {
3477      "name": "ClaimTokenIncentivesEvent",
3478      "discriminator": [
3479        79,
3480        172,
3481        246,
3482        49,
3483        205,
3484        91,
3485        206,
3486        232
3487      ]
3488    },
3489    {
3490      "name": "CloseUserVolumeAccumulatorEvent",
3491      "discriminator": [
3492        146,
3493        159,
3494        189,
3495        172,
3496        146,
3497        88,
3498        56,
3499        244
3500      ]
3501    },
3502    {
3503      "name": "CollectCreatorFeeEvent",
3504      "discriminator": [
3505        122,
3506        2,
3507        127,
3508        1,
3509        14,
3510        191,
3511        12,
3512        175
3513      ]
3514    },
3515    {
3516      "name": "CompleteEvent",
3517      "discriminator": [
3518        95,
3519        114,
3520        97,
3521        156,
3522        212,
3523        46,
3524        152,
3525        8
3526      ]
3527    },
3528    {
3529      "name": "CompletePumpAmmMigrationEvent",
3530      "discriminator": [
3531        189,
3532        233,
3533        93,
3534        185,
3535        92,
3536        148,
3537        234,
3538        148
3539      ]
3540    },
3541    {
3542      "name": "CreateEvent",
3543      "discriminator": [
3544        27,
3545        114,
3546        169,
3547        77,
3548        222,
3549        235,
3550        99,
3551        118
3552      ]
3553    },
3554    {
3555      "name": "ExtendAccountEvent",
3556      "discriminator": [
3557        97,
3558        97,
3559        215,
3560        144,
3561        93,
3562        146,
3563        22,
3564        124
3565      ]
3566    },
3567    {
3568      "name": "InitUserVolumeAccumulatorEvent",
3569      "discriminator": [
3570        134,
3571        36,
3572        13,
3573        72,
3574        232,
3575        101,
3576        130,
3577        216
3578      ]
3579    },
3580    {
3581      "name": "SetCreatorEvent",
3582      "discriminator": [
3583        237,
3584        52,
3585        123,
3586        37,
3587        245,
3588        251,
3589        72,
3590        210
3591      ]
3592    },
3593    {
3594      "name": "SetMetaplexCreatorEvent",
3595      "discriminator": [
3596        142,
3597        203,
3598        6,
3599        32,
3600        127,
3601        105,
3602        191,
3603        162
3604      ]
3605    },
3606    {
3607      "name": "SetParamsEvent",
3608      "discriminator": [
3609        223,
3610        195,
3611        159,
3612        246,
3613        62,
3614        48,
3615        143,
3616        131
3617      ]
3618    },
3619    {
3620      "name": "SyncUserVolumeAccumulatorEvent",
3621      "discriminator": [
3622        197,
3623        122,
3624        167,
3625        124,
3626        116,
3627        81,
3628        91,
3629        255
3630      ]
3631    },
3632    {
3633      "name": "TradeEvent",
3634      "discriminator": [
3635        189,
3636        219,
3637        127,
3638        211,
3639        78,
3640        230,
3641        97,
3642        238
3643      ]
3644    },
3645    {
3646      "name": "UpdateGlobalAuthorityEvent",
3647      "discriminator": [
3648        182,
3649        195,
3650        137,
3651        42,
3652        35,
3653        206,
3654        207,
3655        247
3656      ]
3657    }
3658  ],
3659  "errors": [
3660    {
3661      "code": 6000,
3662      "name": "NotAuthorized",
3663      "msg": "The given account is not authorized to execute this instruction."
3664    },
3665    {
3666      "code": 6001,
3667      "name": "AlreadyInitialized",
3668      "msg": "The program is already initialized."
3669    },
3670    {
3671      "code": 6002,
3672      "name": "TooMuchSolRequired",
3673      "msg": "slippage: Too much SOL required to buy the given amount of tokens."
3674    },
3675    {
3676      "code": 6003,
3677      "name": "TooLittleSolReceived",
3678      "msg": "slippage: Too little SOL received to sell the given amount of tokens."
3679    },
3680    {
3681      "code": 6004,
3682      "name": "MintDoesNotMatchBondingCurve",
3683      "msg": "The mint does not match the bonding curve."
3684    },
3685    {
3686      "code": 6005,
3687      "name": "BondingCurveComplete",
3688      "msg": "The bonding curve has completed and liquidity migrated to raydium."
3689    },
3690    {
3691      "code": 6006,
3692      "name": "BondingCurveNotComplete",
3693      "msg": "The bonding curve has not completed."
3694    },
3695    {
3696      "code": 6007,
3697      "name": "NotInitialized",
3698      "msg": "The program is not initialized."
3699    },
3700    {
3701      "code": 6008,
3702      "name": "WithdrawTooFrequent",
3703      "msg": "Withdraw too frequent"
3704    },
3705    {
3706      "code": 6009,
3707      "name": "NewSizeShouldBeGreaterThanCurrentSize",
3708      "msg": "new_size should be > current_size"
3709    },
3710    {
3711      "code": 6010,
3712      "name": "AccountTypeNotSupported",
3713      "msg": "Account type not supported"
3714    },
3715    {
3716      "code": 6011,
3717      "name": "InitialRealTokenReservesShouldBeLessThanTokenTotalSupply",
3718      "msg": "initial_real_token_reserves should be less than token_total_supply"
3719    },
3720    {
3721      "code": 6012,
3722      "name": "InitialVirtualTokenReservesShouldBeGreaterThanInitialRealTokenReserves",
3723      "msg": "initial_virtual_token_reserves should be greater than initial_real_token_reserves"
3724    },
3725    {
3726      "code": 6013,
3727      "name": "FeeBasisPointsGreaterThanMaximum",
3728      "msg": "fee_basis_points greater than maximum"
3729    },
3730    {
3731      "code": 6014,
3732      "name": "AllZerosWithdrawAuthority",
3733      "msg": "Withdraw authority cannot be set to System Program ID"
3734    },
3735    {
3736      "code": 6015,
3737      "name": "PoolMigrationFeeShouldBeLessThanFinalRealSolReserves",
3738      "msg": "pool_migration_fee should be less than final_real_sol_reserves"
3739    },
3740    {
3741      "code": 6016,
3742      "name": "PoolMigrationFeeShouldBeGreaterThanCreatorFeePlusMaxMigrateFees",
3743      "msg": "pool_migration_fee should be greater than creator_fee + MAX_MIGRATE_FEES"
3744    },
3745    {
3746      "code": 6017,
3747      "name": "DisabledWithdraw",
3748      "msg": "Migrate instruction is disabled"
3749    },
3750    {
3751      "code": 6018,
3752      "name": "DisabledMigrate",
3753      "msg": "Migrate instruction is disabled"
3754    },
3755    {
3756      "code": 6019,
3757      "name": "InvalidCreator",
3758      "msg": "Invalid creator pubkey"
3759    },
3760    {
3761      "code": 6020,
3762      "name": "BuyZeroAmount",
3763      "msg": "Buy zero amount"
3764    },
3765    {
3766      "code": 6021,
3767      "name": "NotEnoughTokensToBuy",
3768      "msg": "Not enough tokens to buy"
3769    },
3770    {
3771      "code": 6022,
3772      "name": "SellZeroAmount",
3773      "msg": "Sell zero amount"
3774    },
3775    {
3776      "code": 6023,
3777      "name": "NotEnoughTokensToSell",
3778      "msg": "Not enough tokens to sell"
3779    },
3780    {
3781      "code": 6024,
3782      "name": "Overflow",
3783      "msg": "Overflow"
3784    },
3785    {
3786      "code": 6025,
3787      "name": "Truncation",
3788      "msg": "Truncation"
3789    },
3790    {
3791      "code": 6026,
3792      "name": "DivisionByZero",
3793      "msg": "Division by zero"
3794    },
3795    {
3796      "code": 6027,
3797      "name": "NotEnoughRemainingAccounts",
3798      "msg": "Not enough remaining accounts"
3799    },
3800    {
3801      "code": 6028,
3802      "name": "AllFeeRecipientsShouldBeNonZero",
3803      "msg": "All fee recipients should be non-zero"
3804    },
3805    {
3806      "code": 6029,
3807      "name": "UnsortedNotUniqueFeeRecipients",
3808      "msg": "Unsorted or not unique fee recipients"
3809    },
3810    {
3811      "code": 6030,
3812      "name": "CreatorShouldNotBeZero",
3813      "msg": "Creator should not be zero"
3814    },
3815    {
3816      "code": 6031,
3817      "name": "StartTimeInThePast"
3818    },
3819    {
3820      "code": 6032,
3821      "name": "EndTimeInThePast"
3822    },
3823    {
3824      "code": 6033,
3825      "name": "EndTimeBeforeStartTime"
3826    },
3827    {
3828      "code": 6034,
3829      "name": "TimeRangeTooLarge"
3830    },
3831    {
3832      "code": 6035,
3833      "name": "EndTimeBeforeCurrentDay"
3834    },
3835    {
3836      "code": 6036,
3837      "name": "SupplyUpdateForFinishedRange"
3838    },
3839    {
3840      "code": 6037,
3841      "name": "DayIndexAfterEndIndex"
3842    },
3843    {
3844      "code": 6038,
3845      "name": "DayInActiveRange"
3846    },
3847    {
3848      "code": 6039,
3849      "name": "InvalidIncentiveMint"
3850    }
3851  ],
3852  "types": [
3853    {
3854      "name": "AdminSetCreatorEvent",
3855      "type": {
3856        "kind": "struct",
3857        "fields": [
3858          {
3859            "name": "timestamp",
3860            "type": "i64"
3861          },
3862          {
3863            "name": "admin_set_creator_authority",
3864            "type": "pubkey"
3865          },
3866          {
3867            "name": "mint",
3868            "type": "pubkey"
3869          },
3870          {
3871            "name": "bonding_curve",
3872            "type": "pubkey"
3873          },
3874          {
3875            "name": "old_creator",
3876            "type": "pubkey"
3877          },
3878          {
3879            "name": "new_creator",
3880            "type": "pubkey"
3881          }
3882        ]
3883      }
3884    },
3885    {
3886      "name": "AdminSetIdlAuthorityEvent",
3887      "type": {
3888        "kind": "struct",
3889        "fields": [
3890          {
3891            "name": "idl_authority",
3892            "type": "pubkey"
3893          }
3894        ]
3895      }
3896    },
3897    {
3898      "name": "AdminUpdateTokenIncentivesEvent",
3899      "type": {
3900        "kind": "struct",
3901        "fields": [
3902          {
3903            "name": "start_time",
3904            "type": "i64"
3905          },
3906          {
3907            "name": "end_time",
3908            "type": "i64"
3909          },
3910          {
3911            "name": "day_number",
3912            "type": "u64"
3913          },
3914          {
3915            "name": "token_supply_per_day",
3916            "type": "u64"
3917          },
3918          {
3919            "name": "mint",
3920            "type": "pubkey"
3921          },
3922          {
3923            "name": "seconds_in_a_day",
3924            "type": "i64"
3925          },
3926          {
3927            "name": "timestamp",
3928            "type": "i64"
3929          }
3930        ]
3931      }
3932    },
3933    {
3934      "name": "BondingCurve",
3935      "type": {
3936        "kind": "struct",
3937        "fields": [
3938          {
3939            "name": "virtual_token_reserves",
3940            "type": "u64"
3941          },
3942          {
3943            "name": "virtual_sol_reserves",
3944            "type": "u64"
3945          },
3946          {
3947            "name": "real_token_reserves",
3948            "type": "u64"
3949          },
3950          {
3951            "name": "real_sol_reserves",
3952            "type": "u64"
3953          },
3954          {
3955            "name": "token_total_supply",
3956            "type": "u64"
3957          },
3958          {
3959            "name": "complete",
3960            "type": "bool"
3961          },
3962          {
3963            "name": "creator",
3964            "type": "pubkey"
3965          }
3966        ]
3967      }
3968    },
3969    {
3970      "name": "ClaimTokenIncentivesEvent",
3971      "type": {
3972        "kind": "struct",
3973        "fields": [
3974          {
3975            "name": "user",
3976            "type": "pubkey"
3977          },
3978          {
3979            "name": "mint",
3980            "type": "pubkey"
3981          },
3982          {
3983            "name": "amount",
3984            "type": "u64"
3985          },
3986          {
3987            "name": "timestamp",
3988            "type": "i64"
3989          },
3990          {
3991            "name": "total_claimed_tokens",
3992            "type": "u64"
3993          },
3994          {
3995            "name": "current_sol_volume",
3996            "type": "u64"
3997          }
3998        ]
3999      }
4000    },
4001    {
4002      "name": "CloseUserVolumeAccumulatorEvent",
4003      "type": {
4004        "kind": "struct",
4005        "fields": [
4006          {
4007            "name": "user",
4008            "type": "pubkey"
4009          },
4010          {
4011            "name": "timestamp",
4012            "type": "i64"
4013          },
4014          {
4015            "name": "total_unclaimed_tokens",
4016            "type": "u64"
4017          },
4018          {
4019            "name": "total_claimed_tokens",
4020            "type": "u64"
4021          },
4022          {
4023            "name": "current_sol_volume",
4024            "type": "u64"
4025          },
4026          {
4027            "name": "last_update_timestamp",
4028            "type": "i64"
4029          }
4030        ]
4031      }
4032    },
4033    {
4034      "name": "CollectCreatorFeeEvent",
4035      "type": {
4036        "kind": "struct",
4037        "fields": [
4038          {
4039            "name": "timestamp",
4040            "type": "i64"
4041          },
4042          {
4043            "name": "creator",
4044            "type": "pubkey"
4045          },
4046          {
4047            "name": "creator_fee",
4048            "type": "u64"
4049          }
4050        ]
4051      }
4052    },
4053    {
4054      "name": "CompleteEvent",
4055      "type": {
4056        "kind": "struct",
4057        "fields": [
4058          {
4059            "name": "user",
4060            "type": "pubkey"
4061          },
4062          {
4063            "name": "mint",
4064            "type": "pubkey"
4065          },
4066          {
4067            "name": "bonding_curve",
4068            "type": "pubkey"
4069          },
4070          {
4071            "name": "timestamp",
4072            "type": "i64"
4073          }
4074        ]
4075      }
4076    },
4077    {
4078      "name": "CompletePumpAmmMigrationEvent",
4079      "type": {
4080        "kind": "struct",
4081        "fields": [
4082          {
4083            "name": "user",
4084            "type": "pubkey"
4085          },
4086          {
4087            "name": "mint",
4088            "type": "pubkey"
4089          },
4090          {
4091            "name": "mint_amount",
4092            "type": "u64"
4093          },
4094          {
4095            "name": "sol_amount",
4096            "type": "u64"
4097          },
4098          {
4099            "name": "pool_migration_fee",
4100            "type": "u64"
4101          },
4102          {
4103            "name": "bonding_curve",
4104            "type": "pubkey"
4105          },
4106          {
4107            "name": "timestamp",
4108            "type": "i64"
4109          },
4110          {
4111            "name": "pool",
4112            "type": "pubkey"
4113          }
4114        ]
4115      }
4116    },
4117    {
4118      "name": "CreateEvent",
4119      "type": {
4120        "kind": "struct",
4121        "fields": [
4122          {
4123            "name": "name",
4124            "type": "string"
4125          },
4126          {
4127            "name": "symbol",
4128            "type": "string"
4129          },
4130          {
4131            "name": "uri",
4132            "type": "string"
4133          },
4134          {
4135            "name": "mint",
4136            "type": "pubkey"
4137          },
4138          {
4139            "name": "bonding_curve",
4140            "type": "pubkey"
4141          },
4142          {
4143            "name": "user",
4144            "type": "pubkey"
4145          },
4146          {
4147            "name": "creator",
4148            "type": "pubkey"
4149          },
4150          {
4151            "name": "timestamp",
4152            "type": "i64"
4153          },
4154          {
4155            "name": "virtual_token_reserves",
4156            "type": "u64"
4157          },
4158          {
4159            "name": "virtual_sol_reserves",
4160            "type": "u64"
4161          },
4162          {
4163            "name": "real_token_reserves",
4164            "type": "u64"
4165          },
4166          {
4167            "name": "token_total_supply",
4168            "type": "u64"
4169          }
4170        ]
4171      }
4172    },
4173    {
4174      "name": "ExtendAccountEvent",
4175      "type": {
4176        "kind": "struct",
4177        "fields": [
4178          {
4179            "name": "account",
4180            "type": "pubkey"
4181          },
4182          {
4183            "name": "user",
4184            "type": "pubkey"
4185          },
4186          {
4187            "name": "current_size",
4188            "type": "u64"
4189          },
4190          {
4191            "name": "new_size",
4192            "type": "u64"
4193          },
4194          {
4195            "name": "timestamp",
4196            "type": "i64"
4197          }
4198        ]
4199      }
4200    },
4201    {
4202      "name": "FeeConfig",
4203      "type": {
4204        "kind": "struct",
4205        "fields": [
4206          {
4207            "name": "bump",
4208            "type": "u8"
4209          },
4210          {
4211            "name": "admin",
4212            "type": "pubkey"
4213          },
4214          {
4215            "name": "flat_fees",
4216            "type": {
4217              "defined": {
4218                "name": "Fees"
4219              }
4220            }
4221          },
4222          {
4223            "name": "fee_tiers",
4224            "type": {
4225              "vec": {
4226                "defined": {
4227                  "name": "FeeTier"
4228                }
4229              }
4230            }
4231          }
4232        ]
4233      }
4234    },
4235    {
4236      "name": "FeeTier",
4237      "type": {
4238        "kind": "struct",
4239        "fields": [
4240          {
4241            "name": "market_cap_lamports_threshold",
4242            "type": "u128"
4243          },
4244          {
4245            "name": "fees",
4246            "type": {
4247              "defined": {
4248                "name": "Fees"
4249              }
4250            }
4251          }
4252        ]
4253      }
4254    },
4255    {
4256      "name": "Fees",
4257      "type": {
4258        "kind": "struct",
4259        "fields": [
4260          {
4261            "name": "lp_fee_bps",
4262            "type": "u64"
4263          },
4264          {
4265            "name": "protocol_fee_bps",
4266            "type": "u64"
4267          },
4268          {
4269            "name": "creator_fee_bps",
4270            "type": "u64"
4271          }
4272        ]
4273      }
4274    },
4275    {
4276      "name": "Global",
4277      "type": {
4278        "kind": "struct",
4279        "fields": [
4280          {
4281            "name": "initialized",
4282            "docs": [
4283              "Unused"
4284            ],
4285            "type": "bool"
4286          },
4287          {
4288            "name": "authority",
4289            "type": "pubkey"
4290          },
4291          {
4292            "name": "fee_recipient",
4293            "type": "pubkey"
4294          },
4295          {
4296            "name": "initial_virtual_token_reserves",
4297            "type": "u64"
4298          },
4299          {
4300            "name": "initial_virtual_sol_reserves",
4301            "type": "u64"
4302          },
4303          {
4304            "name": "initial_real_token_reserves",
4305            "type": "u64"
4306          },
4307          {
4308            "name": "token_total_supply",
4309            "type": "u64"
4310          },
4311          {
4312            "name": "fee_basis_points",
4313            "type": "u64"
4314          },
4315          {
4316            "name": "withdraw_authority",
4317            "type": "pubkey"
4318          },
4319          {
4320            "name": "enable_migrate",
4321            "docs": [
4322              "Unused"
4323            ],
4324            "type": "bool"
4325          },
4326          {
4327            "name": "pool_migration_fee",
4328            "type": "u64"
4329          },
4330          {
4331            "name": "creator_fee_basis_points",
4332            "type": "u64"
4333          },
4334          {
4335            "name": "fee_recipients",
4336            "type": {
4337              "array": [
4338                "pubkey",
4339                7
4340              ]
4341            }
4342          },
4343          {
4344            "name": "set_creator_authority",
4345            "type": "pubkey"
4346          },
4347          {
4348            "name": "admin_set_creator_authority",
4349            "type": "pubkey"
4350          }
4351        ]
4352      }
4353    },
4354    {
4355      "name": "GlobalVolumeAccumulator",
4356      "type": {
4357        "kind": "struct",
4358        "fields": [
4359          {
4360            "name": "start_time",
4361            "type": "i64"
4362          },
4363          {
4364            "name": "end_time",
4365            "type": "i64"
4366          },
4367          {
4368            "name": "seconds_in_a_day",
4369            "type": "i64"
4370          },
4371          {
4372            "name": "mint",
4373            "type": "pubkey"
4374          },
4375          {
4376            "name": "total_token_supply",
4377            "type": {
4378              "array": [
4379                "u64",
4380                30
4381              ]
4382            }
4383          },
4384          {
4385            "name": "sol_volumes",
4386            "type": {
4387              "array": [
4388                "u64",
4389                30
4390              ]
4391            }
4392          }
4393        ]
4394      }
4395    },
4396    {
4397      "name": "InitUserVolumeAccumulatorEvent",
4398      "type": {
4399        "kind": "struct",
4400        "fields": [
4401          {
4402            "name": "payer",
4403            "type": "pubkey"
4404          },
4405          {
4406            "name": "user",
4407            "type": "pubkey"
4408          },
4409          {
4410            "name": "timestamp",
4411            "type": "i64"
4412          }
4413        ]
4414      }
4415    },
4416    {
4417      "name": "OptionBool",
4418      "type": {
4419        "kind": "struct",
4420        "fields": [
4421          "bool"
4422        ]
4423      }
4424    },
4425    {
4426      "name": "SetCreatorEvent",
4427      "type": {
4428        "kind": "struct",
4429        "fields": [
4430          {
4431            "name": "timestamp",
4432            "type": "i64"
4433          },
4434          {
4435            "name": "mint",
4436            "type": "pubkey"
4437          },
4438          {
4439            "name": "bonding_curve",
4440            "type": "pubkey"
4441          },
4442          {
4443            "name": "creator",
4444            "type": "pubkey"
4445          }
4446        ]
4447      }
4448    },
4449    {
4450      "name": "SetMetaplexCreatorEvent",
4451      "type": {
4452        "kind": "struct",
4453        "fields": [
4454          {
4455            "name": "timestamp",
4456            "type": "i64"
4457          },
4458          {
4459            "name": "mint",
4460            "type": "pubkey"
4461          },
4462          {
4463            "name": "bonding_curve",
4464            "type": "pubkey"
4465          },
4466          {
4467            "name": "metadata",
4468            "type": "pubkey"
4469          },
4470          {
4471            "name": "creator",
4472            "type": "pubkey"
4473          }
4474        ]
4475      }
4476    },
4477    {
4478      "name": "SetParamsEvent",
4479      "type": {
4480        "kind": "struct",
4481        "fields": [
4482          {
4483            "name": "initial_virtual_token_reserves",
4484            "type": "u64"
4485          },
4486          {
4487            "name": "initial_virtual_sol_reserves",
4488            "type": "u64"
4489          },
4490          {
4491            "name": "initial_real_token_reserves",
4492            "type": "u64"
4493          },
4494          {
4495            "name": "final_real_sol_reserves",
4496            "type": "u64"
4497          },
4498          {
4499            "name": "token_total_supply",
4500            "type": "u64"
4501          },
4502          {
4503            "name": "fee_basis_points",
4504            "type": "u64"
4505          },
4506          {
4507            "name": "withdraw_authority",
4508            "type": "pubkey"
4509          },
4510          {
4511            "name": "enable_migrate",
4512            "type": "bool"
4513          },
4514          {
4515            "name": "pool_migration_fee",
4516            "type": "u64"
4517          },
4518          {
4519            "name": "creator_fee_basis_points",
4520            "type": "u64"
4521          },
4522          {
4523            "name": "fee_recipients",
4524            "type": {
4525              "array": [
4526                "pubkey",
4527                8
4528              ]
4529            }
4530          },
4531          {
4532            "name": "timestamp",
4533            "type": "i64"
4534          },
4535          {
4536            "name": "set_creator_authority",
4537            "type": "pubkey"
4538          },
4539          {
4540            "name": "admin_set_creator_authority",
4541            "type": "pubkey"
4542          }
4543        ]
4544      }
4545    },
4546    {
4547      "name": "SyncUserVolumeAccumulatorEvent",
4548      "type": {
4549        "kind": "struct",
4550        "fields": [
4551          {
4552            "name": "user",
4553            "type": "pubkey"
4554          },
4555          {
4556            "name": "total_claimed_tokens_before",
4557            "type": "u64"
4558          },
4559          {
4560            "name": "total_claimed_tokens_after",
4561            "type": "u64"
4562          },
4563          {
4564            "name": "timestamp",
4565            "type": "i64"
4566          }
4567        ]
4568      }
4569    },
4570    {
4571      "name": "TradeEvent",
4572      "type": {
4573        "kind": "struct",
4574        "fields": [
4575          {
4576            "name": "mint",
4577            "type": "pubkey"
4578          },
4579          {
4580            "name": "sol_amount",
4581            "type": "u64"
4582          },
4583          {
4584            "name": "token_amount",
4585            "type": "u64"
4586          },
4587          {
4588            "name": "is_buy",
4589            "type": "bool"
4590          },
4591          {
4592            "name": "user",
4593            "type": "pubkey"
4594          },
4595          {
4596            "name": "timestamp",
4597            "type": "i64"
4598          },
4599          {
4600            "name": "virtual_sol_reserves",
4601            "type": "u64"
4602          },
4603          {
4604            "name": "virtual_token_reserves",
4605            "type": "u64"
4606          },
4607          {
4608            "name": "real_sol_reserves",
4609            "type": "u64"
4610          },
4611          {
4612            "name": "real_token_reserves",
4613            "type": "u64"
4614          },
4615          {
4616            "name": "fee_recipient",
4617            "type": "pubkey"
4618          },
4619          {
4620            "name": "fee_basis_points",
4621            "type": "u64"
4622          },
4623          {
4624            "name": "fee",
4625            "type": "u64"
4626          },
4627          {
4628            "name": "creator",
4629            "type": "pubkey"
4630          },
4631          {
4632            "name": "creator_fee_basis_points",
4633            "type": "u64"
4634          },
4635          {
4636            "name": "creator_fee",
4637            "type": "u64"
4638          },
4639          {
4640            "name": "track_volume",
4641            "type": "bool"
4642          },
4643          {
4644            "name": "total_unclaimed_tokens",
4645            "type": "u64"
4646          },
4647          {
4648            "name": "total_claimed_tokens",
4649            "type": "u64"
4650          },
4651          {
4652            "name": "current_sol_volume",
4653            "type": "u64"
4654          },
4655          {
4656            "name": "last_update_timestamp",
4657            "type": "i64"
4658          }
4659        ]
4660      }
4661    },
4662    {
4663      "name": "UpdateGlobalAuthorityEvent",
4664      "type": {
4665        "kind": "struct",
4666        "fields": [
4667          {
4668            "name": "global",
4669            "type": "pubkey"
4670          },
4671          {
4672            "name": "authority",
4673            "type": "pubkey"
4674          },
4675          {
4676            "name": "new_authority",
4677            "type": "pubkey"
4678          },
4679          {
4680            "name": "timestamp",
4681            "type": "i64"
4682          }
4683        ]
4684      }
4685    },
4686    {
4687      "name": "UserVolumeAccumulator",
4688      "type": {
4689        "kind": "struct",
4690        "fields": [
4691          {
4692            "name": "user",
4693            "type": "pubkey"
4694          },
4695          {
4696            "name": "needs_claim",
4697            "type": "bool"
4698          },
4699          {
4700            "name": "total_unclaimed_tokens",
4701            "type": "u64"
4702          },
4703          {
4704            "name": "total_claimed_tokens",
4705            "type": "u64"
4706          },
4707          {
4708            "name": "current_sol_volume",
4709            "type": "u64"
4710          },
4711          {
4712            "name": "last_update_timestamp",
4713            "type": "i64"
4714          },
4715          {
4716            "name": "has_total_claimed_tokens",
4717            "type": "bool"
4718          }
4719        ]
4720      }
4721    }
4722  ]
4723}